//
//  NotificationTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import SwiftUI

/**
 * 通知机制测试视图
 * 用于验证积分变更通知和统计刷新是否正常工作
 */
struct NotificationTestView: View {
    
    @StateObject private var dataManager = DataManager.shared
    @State private var notificationLog: [String] = []
    @State private var selectedMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 成员选择
                if !dataManager.members.isEmpty {
                    Picker("选择成员", selection: $selectedMember) {
                        Text("请选择成员").tag(nil as Member?)
                        ForEach(dataManager.members, id: \.self) { member in
                            Text(member.displayName).tag(member as Member?)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding()
                }
                
                // 测试按钮
                VStack(spacing: 15) {
                    Button("给选中成员加10分") {
                        testAddPoints()
                    }
                    .disabled(selectedMember == nil)
                    
                    Button("给选中成员扣5分") {
                        testDeductPoints()
                    }
                    .disabled(selectedMember == nil)
                    
                    Button("全家加分测试") {
                        testFamilyAddPoints()
                    }
                    
                    Button("手动触发CloudKit同步") {
                        testCloudKitSync()
                    }

                    Button("模拟远程数据变更") {
                        testRemoteDataChange()
                    }

                    Button("测试统计刷新通知") {
                        testStatisticsRefresh()
                    }

                    Button("清空日志") {
                        notificationLog.removeAll()
                    }
                }
                .buttonStyle(.bordered)
                
                // 通知日志
                VStack(alignment: .leading, spacing: 10) {
                    Text("通知日志:")
                        .font(.headline)
                    
                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 5) {
                            ForEach(Array(notificationLog.enumerated()), id: \.offset) { index, log in
                                Text("\(index + 1). \(log)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .frame(maxHeight: 200)
                    .border(Color.gray.opacity(0.3))
                }
                .padding()
                
                Spacer()
            }
            .navigationTitle("通知机制测试")
            .onAppear {
                setupNotificationObservers()
                if selectedMember == nil && !dataManager.members.isEmpty {
                    selectedMember = dataManager.members.first
                }
            }
        }
    }
    
    // MARK: - 测试方法
    
    private func testAddPoints() {
        guard let member = selectedMember else { return }
        dataManager.addPointRecord(to: member, reason: "测试加分", value: 10)
        addLog("执行：给\(member.displayName)加10分")
    }
    
    private func testDeductPoints() {
        guard let member = selectedMember else { return }
        dataManager.addPointRecord(to: member, reason: "测试扣分", value: -5)
        addLog("执行：给\(member.displayName)扣5分")
    }
    
    private func testFamilyAddPoints() {
        for member in dataManager.members {
            dataManager.addPointRecord(to: member, reason: "全家测试加分", value: 3)
        }
        dataManager.sendFamilyStatisticsRefreshNotification(triggerSource: "test")
        addLog("执行：全家加3分")
    }
    
    private func testCloudKitSync() {
        NotificationCenter.default.post(
            name: NSNotification.Name("CloudKitSyncCompleted"),
            object: nil
        )
        addLog("执行：模拟CloudKit同步完成")
    }

    private func testRemoteDataChange() {
        NotificationCenter.default.post(
            name: .NSPersistentStoreRemoteChange,
            object: nil
        )
        addLog("执行：模拟CloudKit远程数据变更")
    }

    private func testStatisticsRefresh() {
        dataManager.sendFamilyStatisticsRefreshNotification(triggerSource: "manual_test")
        addLog("执行：手动触发统计刷新通知")
    }
    
    // MARK: - 通知监听
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            forName: .memberPointsDidChange,
            object: nil,
            queue: .main
        ) { notification in
            if let userInfo = notification.userInfo,
               let memberId = userInfo[NotificationUserInfoKey.memberId] as? String,
               let pointsChange = userInfo[NotificationUserInfoKey.pointsChange] as? Int,
               let reason = userInfo[NotificationUserInfoKey.reason] as? String {
                addLog("收到通知：成员积分变更 - 成员ID:\(memberId.prefix(8))..., 变更:\(pointsChange), 原因:\(reason)")
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .familyStatisticsNeedsRefresh,
            object: nil,
            queue: .main
        ) { notification in
            if let userInfo = notification.userInfo,
               let triggerSource = userInfo[NotificationUserInfoKey.triggerSource] as? String {
                addLog("收到通知：家庭统计刷新 - 触发源:\(triggerSource)")
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CloudKitSyncCompleted"),
            object: nil,
            queue: .main
        ) { _ in
            addLog("收到通知：CloudKit同步完成")
        }
    }
    
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        notificationLog.append("[\(timestamp)] \(message)")
    }
}

#Preview {
    NotificationTestView()
}
